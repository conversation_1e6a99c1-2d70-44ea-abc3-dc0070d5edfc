PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Alamofire (5.5.0)
  - AlipaySDK-iOS (15.8.10)
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - AMapNavi (10.0.600):
    - AMapFoundation (>= 1.8.2)
  - AMapSearch (9.7.0):
    - AMapFoundation (>= 1.8.0)
  - apaSdk (0.1.9.0423.01):
    - CarControlSDK
    - libpag
    - RxSwift
    - SSZipArchive
  - AvoidCrash (2.5.2)
  - Block-KVO (3.0)
  - BusinessRoute (0.0.1):
    - SLRouter
    - SLServiceProtocol
  - CAAFOnlineSDK (1.0.5):
    - HandyJSON (~> 5.0.2)
    - MBProgressHUD (~> 1.2.0)
    - SnapKit (~> 5.6.0)
  - Cache (7.4.0)
  - CACoreBridge (1.2.1):
    - AFNetworking/Reachability (>= 4.0.1)
    - CADefines
    - SLEmptyViewManager
    - YPNavigationBarTransition (>= 2.2.3)
  - CACustomerServiceModule (1.1.7):
    - CACoreBridge
    - CTAssetsPickerController (~> 3.3.1)
    - SDWebImage
  - CADefines (1.1.7)
  - CALaunchAd (1.1.1):
    - libpag (>= ********)
  - CALocation (0.1.0):
    - AMapLocation
    - AMapNavi
    - AMapSearch
    - SLBaseConfig
  - CarControlManager (1.9.2):
    - Alamofire
    - CarControlManager/CarControlCenter (= 1.9.2)
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/CarControlCenter (1.9.2):
    - Alamofire
    - CarControlManager/ControlDataCenter
    - CarControlManager/HttpEngine
    - CarControlManager/MQTTEngine
    - CarControlManager/MQTTUtils
    - CarControlManager/SLCCarCenter
    - CarControlManager/SLCLog
    - CarControlManager/SLCUserCenter
    - CarControlManager/VotMQTTEngine
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/ControlDataCenter (1.9.2):
    - Alamofire
    - CarControlManager/SLCLog
    - Moya
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/HttpEngine (1.9.2):
    - Alamofire
    - CarControlManager/ControlDataCenter
    - CarControlManager/SLCCarCenter
    - CarControlManager/SLCUserCenter
    - CarControlManager/TokenPlugin
    - Moya
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLSimpleNetworking/SLNetworkingCodable
    - SLSimpleNetworking/SLNetworkingPlugin
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
    - Then
  - CarControlManager/MQTTEngine (1.9.2):
    - Alamofire
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/MQTTUtils (1.9.2):
    - Alamofire
    - CarControlManager/SLCCarCenter
    - CarControlManager/SLCUserCenter
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/SLCCarCenter (1.9.2):
    - Alamofire
    - CarControlManager/SLCLog
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/SLCLog (1.9.2):
    - Alamofire
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/SLCUserCenter (1.9.2):
    - Alamofire
    - CarControlManager/ControlDataCenter
    - CarControlManager/SLCLog
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/TokenPlugin (1.9.2):
    - Alamofire
    - Moya
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlManager/VotMQTTEngine (1.9.2):
    - Alamofire
    - CarControlManager/ControlDataCenter
    - CarControlManager/MQTTUtils
    - CarControlManager/SLCCarCenter
    - CarControlManager/SLCUserCenter
    - CarControlManager/TokenPlugin
    - RxCocoa
    - RxRelay
    - RxSwift
    - SLCleanJSONDecoder
    - SLMQTTModel (>= 1.1.4)
    - SLRxSwiftExtension
    - SLSafeUtils (>= 1.0.3)
    - SLSimpleNetworking (>= 1.1.6)
    - SLSimpleNetworking/SLNetworkingCodable
    - SLSimpleNetworking/SLNetworkingPlugin
    - SLTokenManager
    - SLUserDefaultsStore (>= 1.0.4)
    - SLUtils (>= 1.4.5)
  - CarControlSDK (0.4.11.0524.01):
    - AFNetworking (= 4.0.1)
    - Alamofire (= 5.5.0)
    - AMapLocation
    - AMapNavi
    - AMapSearch
    - CertificateSigningRequest
    - Charts (= 4.1.0)
    - CleanJSON
    - CocoaAsyncSocket
    - CryptoSwift (= 1.8.3)
    - EmptyDataSet-Swift (~> 5.0.0)
    - FMDB
    - GKPhotoBrowser (= 2.6.3)
    - HandyJSON (~> 5.0.2)
    - IQKeyboardManager (= 6.5.10)
    - JXSegmentedView
    - KeychainAccess (~> 4.2.2)
    - libpag
    - MetalPetal/Swift
    - MGJRouter_Swift (~> 0.1.3)
    - MJRefresh
    - MMKV (= 1.2.5)
    - Moya (= 15.0.0)
    - Moya/RxSwift
    - ReactorKit
    - RxCocoa
    - RxDataSources
    - RxSwift (= 6.7.0)
    - RxViewController
    - SDCycleScrollView (~> 1.82)
    - SDWebImage (= 5.18.7)
    - SLBaseViewKit (>= 1.1.5)
    - SLCarModel3DPlayer (= 1.2.8)
    - SLCFSPagerView
    - SLLogger (>= 1.0.9)
    - SLLogPlugin
    - SLMQTT (= *******)
    - SLPopViewManager (>= 1.0.4)
    - SLRxSwiftExtension (>= 1.0.2)
    - SLSafeUtils (>= 1.0.3)
    - SLUnityFramework (= 1.4.3)
    - SLUserDefaultsStore (>= 1.0.6)
    - SLUtils (>= 1.4.3)
    - SLWidgetControl (>= 1.6.0)
    - SnapKit (= 5.6.0)
    - SSZipArchive (= 2.4.3)
    - SVGKit
    - SVProgressHUD
    - SwiftDate
    - SwifterSwift (= 6.2.0)
    - SwiftyJSON
    - SwiftyRSA
    - TABAnimated (= 2.6.5)
    - Then
    - Toast-Swift
    - TTGTagCollectionView
    - TXLiteAVSDK_TRTC (= 11.6.15007)
    - UAVPSDK (= **********)
    - VideoDecoder
  - CertificateSigningRequest (1.30.0)
  - Charts (4.1.0):
    - Charts/Core (= 4.1.0)
  - Charts/Core (4.1.0):
    - SwiftAlgorithms (~> 1.0)
  - CleanJSON (1.0.9)
  - CocoaAsyncSocket (7.6.5)
  - CocoaLumberjack (3.7.4):
    - CocoaLumberjack/Core (= 3.7.4)
  - CocoaLumberjack/Core (3.7.4)
  - CocoaMQTT (2.1.6):
    - CocoaMQTT/Core (= 2.1.6)
  - CocoaMQTT/Core (2.1.6):
    - MqttCocoaAsyncSocket (~> 1.0.8)
  - CryptoSwift (1.8.3)
  - CTAssetsPickerController (3.3.1):
    - PureLayout (~> 3.0.0)
  - DeepalAssets (0.1.0)
  - DeepalCommunity (0.1.0):
    - AFNetworking
    - DeepalAssets
    - DeepalShield
    - EmptyDataSet-Swift
    - HUDExtension
    - MMKV
    - RxExtension
    - SDCycleScrollView
    - SDWebImage
    - SLBaseUI
    - SLBuryPointConfig
    - SLColor
    - SLIconFont
    - SLNetworking
    - SLPagingView
    - SLShareImage
    - SLTool
    - SLUserInfo
    - SnapKit
    - YYText-Nice
  - DeepalFilter (0.0.1):
    - BusinessRoute
    - HUDExtension
    - MJRefresh
    - RxDataSources
    - RxExtension
    - RxSwift
    - SLBaseUI
    - SLColor
    - SLIconFont
    - SLNetworking
    - SLRouter
    - SLShareViewModule
    - SLUIKitObjc
    - Toast-Swift
    - TTGTagCollectionView
  - DeepalLogin (0.1.0):
    - BusinessRoute
    - CocoaLumberjack
    - HUDExtension
    - Masonry
    - ReactiveObjC
    - RxExtension
    - RxRelay
    - RxSwiftExt
    - SDWebImage
    - SLBaseConfig
    - SLBaseModel
    - SLBaseUI
    - SLBuryPointConfig
    - SLColor
    - SLFoundation
    - SLIconFont
    - SLLogConfig
    - SLRouter
    - SLSectionModule
    - SLServiceProtocol
    - SLTool
    - SLUIKitObjc
    - SnapKit
    - UIComponent
    - WechatOpenSDK-XCFramework
    - YPNavigationBarTransition
    - YYText-Nice
  - DeepalPhotoBrowser (0.1.0):
    - GKPhotoBrowser
    - HUDExtension
    - SDWebImage
    - SLBaseUI
    - SLColor
    - SLIconFont
    - SLTool
    - SnapKit
  - DeepalPlayer (0.1.0):
    - ZFPlayer
    - ZFPlayer/AVPlayer
    - ZFPlayer/ControlView
  - DeepalSearch (0.1.0):
    - BusinessRoute
    - DeepalAssets
    - HUDExtension
    - MJRefresh
    - MMKV
    - PinLayout
    - RxDataSources
    - RxExtension
    - RxSwift
    - RxSwiftExt
    - SLBaseUI
    - SLBuryPointConfig
    - SLColor
    - SLFoundation
    - SLIconFont
    - SLNetworking
    - SLRouter
    - SLSectionModule
    - SLTool
    - SLUIKitObjc
    - Toast-Swift
  - DeepalShield (0.1.0):
    - Cache
    - EmptyDataSet-Swift
    - SDWebImage
    - SLBaseUI
    - SLColor
    - SLIconFont
    - SLTool
    - SnapKit
  - DeepalSlider (0.1.0):
    - SLUIKitObjc
  - Differentiator (5.0.0)
  - dsBridge (4.0.0)
  - DSF_QRCode (21.2.0):
    - SwiftImageReadWrite (~> 1.9.1)
    - SwiftQRCodeGenerator (~> 2.0.2)
  - EmptyDataSet-Swift (5.0.0)
  - Factory (2.3.2)
  - fishhook (0.2)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - Foil (5.0.1)
  - FSPagerView (0.8.3)
  - FunctionalObjC (1.0.2)
  - GKCycleScrollView (1.2.3)
  - GKPhotoBrowser (2.6.3):
    - GKPhotoBrowser/Default (= 2.6.3)
  - GKPhotoBrowser/AVPlayer (2.6.3):
    - GKPhotoBrowser/Core
    - GKSliderView
  - GKPhotoBrowser/Core (2.6.3)
  - GKPhotoBrowser/Default (2.6.3):
    - GKPhotoBrowser/AVPlayer
    - GKPhotoBrowser/Progress
    - GKPhotoBrowser/SD
  - GKPhotoBrowser/Progress (2.6.3):
    - GKPhotoBrowser/Core
    - GKSliderView
  - GKPhotoBrowser/SD (2.6.3):
    - GKPhotoBrowser/Core
    - SDWebImage (~> 5.0)
  - GKSliderView (1.2.3)
  - GrowingAnalytics-cdp/Autotracker (3.4.1):
    - GrowingAnalytics-cdp/TrackerCore (= 3.4.1)
    - GrowingAnalytics/AutotrackerCore (= 3.4.1)
    - GrowingAnalytics/DefaultServices (= 3.4.1)
    - GrowingAnalytics/Hybrid (= 3.4.1)
    - GrowingAnalytics/MobileDebugger (= 3.4.1)
    - GrowingAnalytics/WebCircle (= 3.4.1)
  - GrowingAnalytics-cdp/TrackerCore (3.4.1):
    - GrowingAnalytics/TrackerCore (= 3.4.1)
  - GrowingAnalytics/AutotrackerCore (3.4.1):
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/Compression (3.4.1):
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/Database (3.4.1):
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/DefaultServices (3.4.1):
    - GrowingAnalytics/Compression
    - GrowingAnalytics/Database
    - GrowingAnalytics/Encryption
    - GrowingAnalytics/Network
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/DISABLE_IDFA (3.4.1):
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/Encryption (3.4.1):
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/Hybrid (3.4.1):
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/MobileDebugger (3.4.1):
    - GrowingAnalytics/TrackerCore
    - GrowingAnalytics/WebSocket
  - GrowingAnalytics/Network (3.4.1):
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/Protobuf (3.4.1):
    - GrowingAnalytics/Database
    - GrowingAnalytics/Protobuf/Proto (= 3.4.1)
    - GrowingAnalytics/TrackerCore
  - GrowingAnalytics/Protobuf/Proto (3.4.1):
    - GrowingAnalytics/Database
    - GrowingAnalytics/TrackerCore
    - Protobuf
  - GrowingAnalytics/TrackerCore (3.4.1)
  - GrowingAnalytics/WebCircle (3.4.1):
    - GrowingAnalytics/AutotrackerCore
    - GrowingAnalytics/Hybrid
    - GrowingAnalytics/WebSocket
  - GrowingAnalytics/WebSocket (3.4.1):
    - GrowingAnalytics/TrackerCore
  - HandyJSON (5.0.2)
  - HUDExtension (0.0.2):
    - libpag
    - MBProgressHUD
    - Prelude
    - SLColor
    - SnapKit
    - Toast-Swift
    - URLNavigator
  - IconFont (1.0.2)
  - IGListDiffKit (5.0.0)
  - IGListKit (5.0.0):
    - IGListDiffKit (= 5.0.0)
  - IQKeyboardManager (6.5.10)
  - JXCategoryView (1.6.1)
  - JXSegmentedView (1.4.1)
  - KeychainAccess (4.2.2)
  - Kingfisher (7.12.0)
  - libpag (4.4.25)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - LookinServer/Core (1.2.8)
  - LookinServer/SwiftAndNoHook (1.2.8):
    - LookinServer/Core
  - lottie-ios (3.4.1)
  - Masonry (1.1.0)
  - MBProgressHUD (1.2.0)
  - MemoryCache (0.1.0):
    - Cache
  - MetalPetal/Core (1.25.2)
  - MetalPetal/Swift (1.25.2):
    - MetalPetal/Core
  - MGJRouter_Swift (0.1.3)
  - MJExtension (3.4.1)
  - MJRefresh (3.7.5)
  - MMKV (1.2.5):
    - MMKVCore (~> 1.2.5)
  - MMKVCore (1.2.16)
  - Moya (15.0.0):
    - Moya/Core (= 15.0.0)
  - Moya/Core (15.0.0):
    - Alamofire (~> 5.0)
  - Moya/RxSwift (15.0.0):
    - Moya/Core
    - RxSwift (~> 6.0)
  - MqttCocoaAsyncSocket (1.0.8)
  - OpenSSL-Universal (3.3.3001)
  - PanModal (0.1.0)
  - PermissionsKit (11.0.0):
    - PermissionsKit/Core (= 11.0.0)
  - PermissionsKit/BluetoothPermission (11.0.0):
    - PermissionsKit/Core
  - PermissionsKit/CameraPermission (11.0.0):
    - PermissionsKit/Core
  - PermissionsKit/Core (11.0.0)
  - PermissionsKit/LocationPermission (11.0.0):
    - PermissionsKit/Core
  - PermissionsKit/MicrophonePermission (11.0.0):
    - PermissionsKit/Core
  - PermissionsKit/NotificationPermission (11.0.0):
    - PermissionsKit/Core
  - PermissionsKit/PhotoLibraryPermission (11.0.0):
    - PermissionsKit/Core
  - PinLayout (1.10.5)
  - Prelude (1.1.1)
  - Protobuf (3.29.4)
  - PureLayout (3.0.2)
  - QAPM (5.3.3)
  - QCloudCore (6.4.5):
    - QCloudCore/Default (= 6.4.5)
  - QCloudCore/Default (6.4.5):
    - QCloudTrack/Beacon (= 6.4.5)
  - QCloudCOSXML (6.4.5):
    - QCloudCOSXML/Default (= 6.4.5)
  - QCloudCOSXML/Default (6.4.5):
    - QCloudCore (= 6.4.5)
  - QCloudTrack/Beacon (6.4.5)
  - ReactiveObjC (3.1.1)
  - ReactorKit (3.2.0):
    - RxSwift (~> 6.0)
    - WeakMapTable (~> 1.1)
  - RpaSdk (0.3.20.016):
    - CarControlSDK
    - lottie-ios (= 3.4.1)
    - SnapKit
  - RxCocoa (6.7.0):
    - RxRelay (= 6.7.0)
    - RxSwift (= 6.7.0)
  - RxDataSources (5.0.0):
    - Differentiator (~> 5.0)
    - RxCocoa (~> 6.0)
    - RxSwift (~> 6.0)
  - RxExtension (6.7.1):
    - MJRefresh
    - RxCocoa
    - RxSwift
    - Toast-Swift
    - URLNavigator
  - RxRelay (6.7.0):
    - RxSwift (= 6.7.0)
  - RxSwift (6.7.0)
  - RxSwiftExt (6.2.1):
    - RxSwiftExt/Core (= 6.2.1)
    - RxSwiftExt/RxCocoa (= 6.2.1)
  - RxSwiftExt/Core (6.2.1):
    - RxSwift (~> 6.0)
  - RxSwiftExt/RxCocoa (6.2.1):
    - RxCocoa (~> 6.0)
    - RxSwiftExt/Core
  - RxViewController (2.0.0):
    - RxCocoa (~> 6.0)
    - RxSwift (~> 6.0)
  - ScreenMapping (0.1.0):
    - Alamofire
    - Masonry
    - OpenSSL-Universal
    - SDWebImage
    - SDWebImageWebPCoder
  - SDCycleScrollView (1.82):
    - SDWebImage (>= 5.0.0)
  - SDWebImage (5.18.7):
    - SDWebImage/Core (= 5.18.7)
  - SDWebImage/Core (5.18.7)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - ServiceModule (0.1.0):
    - AMapLocation
    - AMapNavi
    - AMapSearch
    - BusinessRoute
    - CALocation
    - CarControlSDK
    - Foil
    - GKCycleScrollView
    - HUDExtension
    - libpag
    - MemoryCache
    - MJRefresh
    - MMKV
    - Prelude
    - RxCocoa
    - RxExtension
    - RxRelay
    - RxSwift
    - RxSwiftExt
    - SLBaseUI
    - SLBuryPointConfig
    - SLColor
    - SLGeolocationService
    - SLIconFont
    - SLMapView
    - SLNetworking
    - SLRouter
    - SLSectionModule
    - SLServiceProtocol
    - SLShareViewModule
    - SnapKit
    - SwifterSwift/UIKit
    - Toast-Swift
  - SFImageMaker (1.1.3)
  - SLActivity (0.1.0):
    - BusinessRoute
    - CALocation
    - DeepalFilter
    - EmptyDataSet-Swift
    - Factory
    - GKCycleScrollView
    - IGListDiffKit
    - IGListKit
    - IQKeyboardManager
    - JXSegmentedView
    - MJRefresh
    - RxExtension
    - RxSwift
    - SDCycleScrollView
    - SLBaseUI
    - SLBuryPointConfig
    - SLColor
    - SLEmptyViewManager
    - SLFoundation
    - SLIconFont
    - SLNetworking
    - SLPagingView
    - SLPopViewManager
    - SLRouter
    - SLSectionModule
    - SLShareImage
    - SLTool
    - SLUserInfo
  - SLBaseConfig (0.1.0):
    - SLEnvConfig
  - SLBaseModel (0.3.0):
    - SLFoundation
    - YYModel
  - SLBaseUI (0.2.0):
    - DeepalAssets
    - GKPhotoBrowser
    - JXSegmentedView
    - libpag
    - Masonry
    - MJRefresh
    - Prelude (~> 1.1.1)
    - RxExtension
    - SDCycleScrollView
    - SDWebImage
    - SLColor
    - SLEmptyViewManager
    - SLFoundation
    - SLIconFont
    - SLNetworking
    - SLPagingView
    - SLTool
    - SnapKit
    - UIComponent
  - SLBaseViewKit (1.2.0)
  - SLBuffer (1.0.2)
  - SLBuryPointConfig (0.1.0):
    - CADefines
    - CALocation
    - GrowingAnalytics-cdp/Autotracker
    - SLFoundation
    - SLRouter
    - SLServiceProtocol
  - SLCarModel3DPlayer (1.2.8):
    - SLUnityFramework (>= 1.4.1)
  - SLCFSPagerView (0.8.4)
  - SLCleanJSONDecoder (1.0.0):
    - CleanJSON
  - SLColor (0.1.0):
    - SwifterSwift/UIKit
  - SLEmptyViewManager (1.3.3)
  - SLEnvConfig (1.0.6)
  - SLFoundation (0.2.5)
  - SLGeolocationService (0.1.0):
    - RxCocoa (~> 6.7.0)
    - RxSwift (~> 6.7.0)
  - SLGzip (1.0.6)
  - SLIconFont (0.1.2):
    - SLUIKitObjc
  - SLKeyChain (0.1.1):
    - CADefines
  - SLLogConfig (0.2.0):
    - CocoaLumberjack
  - SLLogger (1.1.3):
    - SLBuffer (>= 1.0.2)
    - SSignalKit (>= 1.0.2)
  - SLLogPlugin (1.0.0)
  - SLMallModule (0.1.0):
    - AFNetworking
    - BusinessRoute
    - DeepalAssets
    - GKCycleScrollView
    - IGListDiffKit
    - IGListKit
    - JXSegmentedView
    - libpag
    - MJRefresh
    - ReactorKit
    - RxExtension
    - RxSwift
    - SLBaseUI
    - SLBuryPointConfig
    - SLColor
    - SLFoundation
    - SLIconFont
    - SLNetworking
    - SLPagingView
    - SLSectionModule
    - SLSectionModule/JX
    - SLTool
    - SLUserInfo
    - TTGTagCollectionView
  - SLMapView (0.1.0):
    - AMapNavi
  - SLMQTT (*******):
    - CocoaMQTT (>= 2.1.6)
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLMQTT/Encryption (= *******)
    - SLMQTT/Log (= *******)
    - SLMQTT/Model (= *******)
    - SLMQTT/Version (= *******)
    - SLUtils (>= 1.3.3)
    - SSignalKit
  - SLMQTT/Encryption (*******):
    - CocoaMQTT (>= 2.1.6)
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLMQTT/Log
    - SLMQTT/Version
    - SLUtils (>= 1.3.3)
    - SSignalKit
  - SLMQTT/Log (*******):
    - CocoaMQTT (>= 2.1.6)
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLMQTT/Version
    - SLUtils (>= 1.3.3)
    - SSignalKit
  - SLMQTT/Model (*******):
    - CocoaMQTT (>= 2.1.6)
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLMQTT/Encryption
    - SLMQTT/Log
    - SLMQTT/Version
    - SLUtils (>= 1.3.3)
    - SSignalKit
  - SLMQTT/Version (*******):
    - CocoaMQTT (>= 2.1.6)
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLUtils (>= 1.3.3)
    - SSignalKit
  - SLMQTTModel (1.1.6):
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLMQTTModel/Model (= 1.1.6)
    - SLUtils (>= 1.3.2)
    - SSignalKit
  - SLMQTTModel/Encryption (1.1.6):
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLMQTTModel/Log
    - SLMQTTModel/Version
    - SLUtils (>= 1.3.2)
    - SSignalKit
  - SLMQTTModel/Log (1.1.6):
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLUtils (>= 1.3.2)
    - SSignalKit
  - SLMQTTModel/Model (1.1.6):
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLMQTTModel/Encryption
    - SLMQTTModel/Log
    - SLMQTTModel/Version
    - SLUtils (>= 1.3.2)
    - SSignalKit
  - SLMQTTModel/Version (1.1.6):
    - CryptoSwift
    - RxSwift
    - SLGzip (>= 1.0.6)
    - SLLogger (>= 1.1.0)
    - SLLogPlugin
    - SLUtils (>= 1.3.2)
    - SSignalKit
  - SLNetworking (0.3.2):
    - SLNetworking/Core (= 0.3.2)
  - SLNetworking/Cache (0.3.2):
    - Cache (~> 7.1)
    - CleanJSON
    - SLNetworking/Codable
    - SLNetworking/Core
    - SLUserInfo
  - SLNetworking/Codable (0.3.2):
    - Cache (~> 7.1)
    - CleanJSON
    - Moya
    - RxSwift
    - SLBaseConfig
    - SLNetworking/Core
  - SLNetworking/Core (0.3.2):
    - Moya
    - Moya/RxSwift
    - SLBaseConfig
    - SLUserInfo
    - SwifterSwift/SwiftStdlib
  - SLNetworking/Plugin (0.3.2):
    - CryptoSwift
    - HUDExtension
    - SLBaseConfig
    - SLNetworking/Core
    - SLUserInfo
    - SwifterSwift/SwiftStdlib
    - Toast-Swift
  - SLPagingView (0.1.0)
  - SLPopViewManager (1.0.5)
  - SLPresent (0.1.0)
  - SLRouter (0.2.3):
    - URLNavigator
  - SLRxSwiftExtension (1.0.2):
    - RxSwift
  - SLSafeUtils (1.0.3)
  - SLSectionModule (0.1.1):
    - SLSectionModule/Core (= 0.1.1)
  - SLSectionModule/Core (0.1.1):
    - IGListDiffKit
    - IGListKit
    - MJRefresh
    - RxExtension
    - RxSwift
    - SLBaseUI
    - SLColor
    - SLIconFont
    - SLShareViewModule
    - SwifterSwift
  - SLSectionModule/JX (0.1.1):
    - RxCocoa
    - SLPagingView
    - SLSectionModule/Core
  - SLServiceProtocol (0.2.2):
    - HUDExtension
    - SLBaseConfig
    - SLBaseModel
    - SLRouter
  - SLShareImage (0.1.0):
    - BusinessRoute
    - DeepalPhotoBrowser
    - DeepalPlayer
    - RxCocoa
    - RxExtension
    - RxSwift
    - SLBaseConfig
    - SLBaseUI
    - SLIconFont
    - SLNetworking
    - SLTool
    - SwiftDate
    - ZFPlayer
  - SLShareViewModule (0.1.0):
    - CADefines
    - SLPresent
    - SLUIKitObjc
    - SnapKit
  - SLSimpleNetworking (1.1.6):
    - SLSimpleNetworking/Core (= 1.1.6)
  - SLSimpleNetworking/Core (1.1.6):
    - Moya
    - Moya/RxSwift
  - SLSimpleNetworking/SLNetworkingCodable (1.1.6):
    - CleanJSON
    - Moya
    - RxSwift
    - SLSimpleNetworking/Core
  - SLSimpleNetworking/SLNetworkingPlugin (1.1.6):
    - CryptoSwift
    - SLSimpleNetworking/Core
    - SLSimpleNetworking/SLNetworkingCodable
  - SLTokenManager (1.0.2):
    - RxSwift
    - SLUtils
  - SLTool (0.1.0):
    - SwiftDate
    - SwifterSwift
  - SLUIKitObjc (0.2.0):
    - CADefines
    - CocoaLumberjack
    - SFImageMaker
    - SLLogConfig
    - YPNavigationBarTransition
  - SLUnityFramework (1.4.3)
  - SLUserDefaultsStore (1.0.6):
    - SLUtils (>= 1.3.0)
  - SLUserInfo (0.2.2):
    - SLBaseConfig
    - SLBaseModel
    - SLBuryPointConfig
    - SLFoundation
    - SLRouter
    - SLServiceProtocol
    - SLWidgetControl
    - YYModel
  - SLUtils (1.5.6)
  - SLWechatModule (0.1.0):
    - CocoaLumberjack
    - SLLogConfig
    - SLUIKitObjc
    - WechatOpenSDK-XCFramework
  - SLWidgetControl (1.6.0):
    - SLUserDefaultsStore
    - SLUtils (>= 1.4.1)
  - SLYJHollowPageControl (0.1.0):
    - Masonry
  - SnapKit (5.6.0)
  - SSignalKit (1.0.2)
  - SSZipArchive (2.4.3)
  - SVGKit (3.0.0):
    - CocoaLumberjack (~> 3.0)
  - SVProgressHUD (2.2.5)
  - SwiftAlgorithms (1.0.0)
  - SwiftDate (7.0.0)
  - SwifterSwift (6.2.0):
    - SwifterSwift/AppKit (= 6.2.0)
    - SwifterSwift/Combine (= 6.2.0)
    - SwifterSwift/CoreAnimation (= 6.2.0)
    - SwifterSwift/CoreGraphics (= 6.2.0)
    - SwifterSwift/CoreLocation (= 6.2.0)
    - SwifterSwift/CryptoKit (= 6.2.0)
    - SwifterSwift/Dispatch (= 6.2.0)
    - SwifterSwift/Foundation (= 6.2.0)
    - SwifterSwift/HealthKit (= 6.2.0)
    - SwifterSwift/MapKit (= 6.2.0)
    - SwifterSwift/SceneKit (= 6.2.0)
    - SwifterSwift/SpriteKit (= 6.2.0)
    - SwifterSwift/StoreKit (= 6.2.0)
    - SwifterSwift/SwiftStdlib (= 6.2.0)
    - SwifterSwift/UIKit (= 6.2.0)
    - SwifterSwift/WebKit (= 6.2.0)
  - SwifterSwift/AppKit (6.2.0)
  - SwifterSwift/Combine (6.2.0)
  - SwifterSwift/CoreAnimation (6.2.0)
  - SwifterSwift/CoreGraphics (6.2.0)
  - SwifterSwift/CoreLocation (6.2.0)
  - SwifterSwift/CryptoKit (6.2.0)
  - SwifterSwift/Dispatch (6.2.0)
  - SwifterSwift/Foundation (6.2.0)
  - SwifterSwift/HealthKit (6.2.0)
  - SwifterSwift/MapKit (6.2.0)
  - SwifterSwift/SceneKit (6.2.0)
  - SwifterSwift/SpriteKit (6.2.0)
  - SwifterSwift/StoreKit (6.2.0)
  - SwifterSwift/SwiftStdlib (6.2.0)
  - SwifterSwift/UIKit (6.2.0)
  - SwifterSwift/WebKit (6.2.0)
  - SwiftGen (6.6.3)
  - SwiftImageReadWrite (1.9.2)
  - SwiftProtobuf (1.29.0)
  - SwiftQRCodeGenerator (2.0.2)
  - SwiftyJSON (5.0.2)
  - SwiftyRSA (1.7.0):
    - SwiftyRSA/ObjC (= 1.7.0)
  - SwiftyRSA/ObjC (1.7.0)
  - TABAnimated (2.6.5)
  - TagListView-ObjC (0.1.1)
  - Then (3.0.0)
  - Toast-Swift (5.1.1)
  - TPNS-iOS (*******)
  - TPNS-iOS-Extension (*******)
  - TTGTagCollectionView (2.4.2)
  - TXLiteAVSDK_TRTC (11.6.15007):
    - TXLiteAVSDK_TRTC/TRTC (= 11.6.15007)
  - TXLiteAVSDK_TRTC/TRTC (11.6.15007)
  - TZImagePickerController (3.8.8):
    - TZImagePickerController/Basic (= 3.8.8)
    - TZImagePickerController/Location (= 3.8.8)
  - TZImagePickerController/Basic (3.8.8)
  - TZImagePickerController/Location (3.8.8)
  - UAVPSDK (**********):
    - SnapKit
    - SSZipArchive
    - SwiftProtobuf
  - UIComponent (4.1.4)
  - "UITableView+FDTemplateLayoutCell (1.6)"
  - UITextView-WZB (1.1.1)
  - URLNavigator (2.6.0)
  - VideoDecoder (1.1.1)
  - WeakMapTable (1.2.0)
  - WechatOpenSDK-XCFramework (2.0.4)
  - Weibo_SDK (3.3.8)
  - YBAttributeTextTapAction (3.0.3)
  - YPNavigationBarTransition (2.2.3)
  - YYCache (1.0.4)
  - YYModel (1.0.4)
  - YYText-Nice (1.1.0)
  - ZFPlayer (4.1.4):
    - ZFPlayer/Core (= 4.1.4)
  - ZFPlayer/AVPlayer (4.1.4):
    - ZFPlayer/Core
  - ZFPlayer/ControlView (4.1.4):
    - ZFPlayer/Core
  - ZFPlayer/Core (4.1.4)

DEPENDENCIES:
  - AFNetworking (= 4.0.1)
  - Alamofire (= 5.5.0)
  - AlipaySDK-iOS (= 15.8.10)
  - AMapLocation (= 2.10.0)
  - AMapNavi (= 10.0.600)
  - AMapSearch (= 9.7.0)
  - "apaSdk (from `****************:deepal/vehicleControlSDK/changan-ev-ios-apa-sdk.git`, branch `dev_318`)"
  - AvoidCrash (~> 2.5.2)
  - Block-KVO (from `https://github.com/Tricertops/Block-KVO.git`, tag `v3.0`)
  - BusinessRoute (from `./LocalPods/BusinessRoute`)
  - CAAFOnlineSDK
  - Cache (= 7.4.0)
  - CACoreBridge (= 1.2.1)
  - CACustomerServiceModule (= 1.1.7)
  - CADefines (= 1.1.7)
  - CALaunchAd (= 1.1.1)
  - CALocation (from `./LocalPods/CALocation`)
  - CarControlManager (= 1.9.2)
  - CarControlSDK (= 0.4.11.0524.01)
  - CleanJSON
  - CocoaAsyncSocket (= 7.6.5)
  - CocoaLumberjack (= 3.7.4)
  - CocoaMQTT (= 2.1.6)
  - CTAssetsPickerController (= 3.3.1)
  - DeepalAssets (from `./LocalPods/DeepalAssets`)
  - DeepalCommunity (from `./LocalPods/DeepalCommunity`)
  - DeepalFilter (from `./LocalPods/DeepalFilter`)
  - DeepalLogin (from `./LocalPods/DeepalLogin`)
  - DeepalPhotoBrowser (from `./LocalPods/DeepalPhotoBrowser`)
  - DeepalPlayer (from `./LocalPods/DeepalPlayer`)
  - DeepalSearch (from `./LocalPods/DeepalSearch`)
  - DeepalShield (from `./LocalPods/DeepalShield`)
  - DeepalSlider (from `./LocalPods/DeepalSlider`)
  - dsBridge (= 4.0.0)
  - DSF_QRCode (~> 21.2.0)
  - EmptyDataSet-Swift (~> 5.0.0)
  - Factory (from `https://github.com/hmlongco/Factory.git`, tag `2.3.2`)
  - fishhook (= 0.2)
  - FMDB (= 2.7.5)
  - Foil (= 5.0.1)
  - FSPagerView (= 0.8.3)
  - FunctionalObjC (= 1.0.2)
  - GKPhotoBrowser (= 2.6.3)
  - GrowingAnalytics-cdp/Autotracker (= 3.4.1)
  - GrowingAnalytics/DISABLE_IDFA (= 3.4.1)
  - GrowingAnalytics/Protobuf (= 3.4.1)
  - HandyJSON (= 5.0.2)
  - HUDExtension (from `./LocalPods/HUDExtension`)
  - IconFont (= 1.0.2)
  - IGListDiffKit (from `https://github.com/Instagram/IGListKit.git`, tag `5.0.0`)
  - IGListKit (from `https://github.com/Instagram/IGListKit.git`, tag `5.0.0`)
  - IQKeyboardManager (= 6.5.10)
  - JXCategoryView (= 1.6.1)
  - JXSegmentedView (= 1.4.1)
  - KeychainAccess (= 4.2.2)
  - Kingfisher (= 7.12.0)
  - libpag (= 4.4.25)
  - LookinServer/SwiftAndNoHook
  - Masonry (= 1.1.0)
  - MBProgressHUD (= 1.2.0)
  - MemoryCache (from `./LocalPods/MemoryCache`)
  - MGJRouter_Swift (= 0.1.3)
  - MJExtension (= 3.4.1)
  - MJRefresh (= 3.7.5)
  - MMKV (= 1.2.5)
  - PanModal (from `./LocalPods/PanModal`)
  - PermissionsKit (from `https://github.com/sparrowcode/PermissionsKit.git`, tag `11.1`)
  - PermissionsKit/BluetoothPermission (from `https://github.com/sparrowcode/PermissionsKit.git`, tag `11.1`)
  - PermissionsKit/CameraPermission (from `https://github.com/sparrowcode/PermissionsKit.git`, tag `11.1`)
  - PermissionsKit/LocationPermission (from `https://github.com/sparrowcode/PermissionsKit.git`, tag `11.1`)
  - PermissionsKit/MicrophonePermission (from `https://github.com/sparrowcode/PermissionsKit.git`, tag `11.1`)
  - PermissionsKit/NotificationPermission (from `https://github.com/sparrowcode/PermissionsKit.git`, tag `11.1`)
  - PermissionsKit/PhotoLibraryPermission (from `https://github.com/sparrowcode/PermissionsKit.git`, tag `11.1`)
  - PinLayout (= 1.10.5)
  - Prelude (= 1.1.1)
  - QAPM (= 5.3.3)
  - QCloudCOSXML (= 6.4.5)
  - ReactiveObjC (= 3.1.1)
  - ReactorKit
  - RpaSdk (from `https://e.coding.net/deepal/vehicleControlSDK/changan-ev-ios-Rpa-sdk.git`, tag `3.20.029-20250331102828`)
  - RxCocoa (= 6.7.0)
  - RxDataSources (= 5.0.0)
  - RxExtension (from `./LocalPods/RxExtension`)
  - RxSwift
  - RxSwift (= 6.7.0)
  - RxSwiftExt (= 6.2.1)
  - ScreenMapping (from `https://e.coding.net/deepal/vehicleControlSDK/ScreenMapping.git`, branch `master`)
  - SDCycleScrollView (= 1.82)
  - SDWebImage (= 5.18.7)
  - ServiceModule (from `./LocalPods/ServiceModule`)
  - SFImageMaker (= 1.1.3)
  - SLActivity (from `./LocalPods/SLActivity`)
  - SLBaseConfig (from `./LocalPods/SLBaseConfig`)
  - SLBaseModel (from `./LocalPods/SLBaseModel`)
  - SLBaseUI (from `./LocalPods/SLBaseUI`)
  - SLBaseViewKit (= 1.2.0)
  - SLBuryPointConfig (from `./LocalPods/SLBuryPointConfig`)
  - SLCleanJSONDecoder
  - SLColor (from `./LocalPods/SLColor`)
  - SLEmptyViewManager (= 1.3.3)
  - SLEnvConfig
  - SLEnvConfig (= 1.0.6)
  - SLFoundation (= 0.2.5)
  - SLGeolocationService (from `./LocalPods/SLGeolocationService`)
  - SLIconFont (from `./LocalPods/SLIconFont`)
  - SLKeyChain (= 0.1.1)
  - SLLogConfig (= 0.2.0)
  - SLLogger (= 1.1.3)
  - SLLogger (>= 1.1.2)
  - SLLogPlugin
  - SLMallModule (from `./LocalPods/SLMallModule`)
  - SLMapView (from `./LocalPods/SLMapView`)
  - SLMQTTModel (= 1.1.6)
  - SLNetworking (from `./LocalPods/SLNetworking`)
  - SLNetworking/Cache (from `./LocalPods/SLNetworking`)
  - SLNetworking/Codable (from `./LocalPods/SLNetworking`)
  - SLNetworking/Plugin (from `./LocalPods/SLNetworking`)
  - SLPagingView (from `./LocalPods/SLPagingView`)
  - SLPopViewManager (= 1.0.5)
  - SLPresent (from `./LocalPods/SLPresent`)
  - SLRouter (from `./LocalPods/SLRouter`)
  - SLSafeUtils (>= 1.0.3)
  - SLSectionModule (from `./LocalPods/SLSectionModule`)
  - SLSectionModule/JX (from `./LocalPods/SLSectionModule`)
  - SLServiceProtocol (from `./LocalPods/SLServiceProtocol`)
  - SLShareImage (from `./LocalPods/SLShareImage`)
  - SLSimpleNetworking (= 1.1.6)
  - SLSimpleNetworking/SLNetworkingCodable (= 1.1.6)
  - SLSimpleNetworking/SLNetworkingPlugin (= 1.1.6)
  - SLTokenManager
  - SLTool (from `./LocalPods/SLTool`)
  - SLUIKitObjc (from `./LocalPods/SLUIKitObjc`)
  - SLUserInfo (from `./LocalPods/SLUserInfo`)
  - SLUtils
  - SLUtils (= 1.5.6)
  - SLWechatModule (from `./LocalPods/SLWechatShareModule`)
  - SLWidgetControl
  - SLWidgetControl (= 1.6.0)
  - SLYJHollowPageControl (from `./LocalPods/SLYJHollowPageControl`)
  - SnapKit (= 5.6.0)
  - SSZipArchive
  - SSZipArchive (= 2.4.3)
  - SVProgressHUD (= 2.2.5)
  - SwiftDate (= 7.0.0)
  - SwifterSwift/CoreAnimation (= 6.2.0)
  - SwifterSwift/CoreGraphics (= 6.2.0)
  - SwifterSwift/Dispatch (= 6.2.0)
  - SwifterSwift/Foundation (= 6.2.0)
  - SwifterSwift/SwiftStdlib (= 6.2.0)
  - SwifterSwift/UIKit (= 6.2.0)
  - SwiftGen (= 6.6.3)
  - TagListView-ObjC (= 0.1.1)
  - Toast-Swift (= 5.1.1)
  - TPNS-iOS (= *******)
  - TPNS-iOS-Extension (= *******)
  - TTGTagCollectionView (= 2.4.2)
  - TXLiteAVSDK_TRTC (= 11.6.15007)
  - TZImagePickerController (= 3.8.8)
  - UIComponent (= 4.1.4)
  - "UITableView+FDTemplateLayoutCell (= 1.6)"
  - UITextView-WZB (= 1.1.1)
  - URLNavigator (= 2.6.0)
  - WechatOpenSDK-XCFramework (= 2.0.4)
  - Weibo_SDK (from `https://github.com/sinaweibosdk/weibo_ios_sdk.git`, tag `3.4.0`)
  - YBAttributeTextTapAction
  - YPNavigationBarTransition (= 2.2.3)
  - YYCache (= 1.0.4)
  - YYModel (= 1.0.4)
  - YYText-Nice (= 1.1.0)
  - ZFPlayer (= 4.1.4)
  - ZFPlayer/AVPlayer (= 4.1.4)
  - ZFPlayer/ControlView (= 4.1.4)

SPEC REPOS:
  "****************:deepal/vehicleControlSDK/SLRepos.git":
    - CarControlManager
    - CarControlSDK
    - SLBaseViewKit
    - SLBuffer
    - SLCarModel3DPlayer
    - SLCFSPagerView
    - SLGzip
    - SLLogger
    - SLLogPlugin
    - SLMQTT
    - SLMQTTModel
    - SLPopViewManager
    - SLRxSwiftExtension
    - SLSafeUtils
    - SLUnityFramework
    - SLUserDefaultsStore
    - SLUtils
    - SLWidgetControl
    - SSignalKit
    - UAVPSDK
  https://e.coding.net/deepal/digital-marketing/changan-ev-ios-specs.git:
    - CAAFOnlineSDK
    - Cache
    - CACoreBridge
    - CACustomerServiceModule
    - CADefines
    - CALaunchAd
    - dsBridge
    - Prelude
    - SLCleanJSONDecoder
    - SLEmptyViewManager
    - SLEnvConfig
    - SLFoundation
    - SLKeyChain
    - SLLogConfig
    - SLShareViewModule
    - SLSimpleNetworking
    - SLTokenManager
    - UIComponent
    - URLNavigator
  https://github.com/CocoaPods/Specs.git:
    - AFNetworking
    - Alamofire
    - AlipaySDK-iOS
    - AMapFoundation
    - AMapLocation
    - AMapNavi
    - AMapSearch
    - AvoidCrash
    - CertificateSigningRequest
    - Charts
    - CleanJSON
    - CocoaAsyncSocket
    - CocoaLumberjack
    - CocoaMQTT
    - CryptoSwift
    - CTAssetsPickerController
    - Differentiator
    - DSF_QRCode
    - EmptyDataSet-Swift
    - fishhook
    - FMDB
    - Foil
    - FSPagerView
    - FunctionalObjC
    - GKCycleScrollView
    - GKPhotoBrowser
    - GKSliderView
    - GrowingAnalytics
    - GrowingAnalytics-cdp
    - HandyJSON
    - IconFont
    - IQKeyboardManager
    - JXCategoryView
    - JXSegmentedView
    - KeychainAccess
    - Kingfisher
    - libpag
    - libwebp
    - LookinServer
    - lottie-ios
    - Masonry
    - MBProgressHUD
    - MetalPetal
    - MGJRouter_Swift
    - MJExtension
    - MJRefresh
    - MMKV
    - MMKVCore
    - Moya
    - MqttCocoaAsyncSocket
    - OpenSSL-Universal
    - PinLayout
    - Protobuf
    - PureLayout
    - QCloudCore
    - QCloudCOSXML
    - QCloudTrack
    - ReactiveObjC
    - ReactorKit
    - RxCocoa
    - RxDataSources
    - RxRelay
    - RxSwift
    - RxSwiftExt
    - RxViewController
    - SDCycleScrollView
    - SDWebImage
    - SDWebImageWebPCoder
    - SFImageMaker
    - SnapKit
    - SSZipArchive
    - SVGKit
    - SVProgressHUD
    - SwiftAlgorithms
    - SwiftDate
    - SwifterSwift
    - SwiftGen
    - SwiftImageReadWrite
    - SwiftProtobuf
    - SwiftQRCodeGenerator
    - SwiftyJSON
    - SwiftyRSA
    - TABAnimated
    - TagListView-ObjC
    - Then
    - Toast-Swift
    - TPNS-iOS
    - TPNS-iOS-Extension
    - TTGTagCollectionView
    - TXLiteAVSDK_TRTC
    - TZImagePickerController
    - "UITableView+FDTemplateLayoutCell"
    - UITextView-WZB
    - VideoDecoder
    - WeakMapTable
    - WechatOpenSDK-XCFramework
    - YBAttributeTextTapAction
    - YPNavigationBarTransition
    - YYCache
    - YYModel
    - YYText-Nice
    - ZFPlayer
  https://github.com/TencentCloud/QAPM-iOS-CocoaPods.git:
    - QAPM

EXTERNAL SOURCES:
  apaSdk:
    :branch: dev_318
    :git: "****************:deepal/vehicleControlSDK/changan-ev-ios-apa-sdk.git"
  Block-KVO:
    :git: https://github.com/Tricertops/Block-KVO.git
    :tag: v3.0
  BusinessRoute:
    :path: "./LocalPods/BusinessRoute"
  CALocation:
    :path: "./LocalPods/CALocation"
  DeepalAssets:
    :path: "./LocalPods/DeepalAssets"
  DeepalCommunity:
    :path: "./LocalPods/DeepalCommunity"
  DeepalFilter:
    :path: "./LocalPods/DeepalFilter"
  DeepalLogin:
    :path: "./LocalPods/DeepalLogin"
  DeepalPhotoBrowser:
    :path: "./LocalPods/DeepalPhotoBrowser"
  DeepalPlayer:
    :path: "./LocalPods/DeepalPlayer"
  DeepalSearch:
    :path: "./LocalPods/DeepalSearch"
  DeepalShield:
    :path: "./LocalPods/DeepalShield"
  DeepalSlider:
    :path: "./LocalPods/DeepalSlider"
  Factory:
    :git: https://github.com/hmlongco/Factory.git
    :tag: 2.3.2
  HUDExtension:
    :path: "./LocalPods/HUDExtension"
  IGListDiffKit:
    :git: https://github.com/Instagram/IGListKit.git
    :tag: 5.0.0
  IGListKit:
    :git: https://github.com/Instagram/IGListKit.git
    :tag: 5.0.0
  MemoryCache:
    :path: "./LocalPods/MemoryCache"
  PanModal:
    :path: "./LocalPods/PanModal"
  PermissionsKit:
    :git: https://github.com/sparrowcode/PermissionsKit.git
    :tag: '11.1'
  RpaSdk:
    :git: https://e.coding.net/deepal/vehicleControlSDK/changan-ev-ios-Rpa-sdk.git
    :tag: 3.20.029-20250331102828
  RxExtension:
    :path: "./LocalPods/RxExtension"
  ScreenMapping:
    :branch: master
    :git: https://e.coding.net/deepal/vehicleControlSDK/ScreenMapping.git
  ServiceModule:
    :path: "./LocalPods/ServiceModule"
  SLActivity:
    :path: "./LocalPods/SLActivity"
  SLBaseConfig:
    :path: "./LocalPods/SLBaseConfig"
  SLBaseModel:
    :path: "./LocalPods/SLBaseModel"
  SLBaseUI:
    :path: "./LocalPods/SLBaseUI"
  SLBuryPointConfig:
    :path: "./LocalPods/SLBuryPointConfig"
  SLColor:
    :path: "./LocalPods/SLColor"
  SLGeolocationService:
    :path: "./LocalPods/SLGeolocationService"
  SLIconFont:
    :path: "./LocalPods/SLIconFont"
  SLMallModule:
    :path: "./LocalPods/SLMallModule"
  SLMapView:
    :path: "./LocalPods/SLMapView"
  SLNetworking:
    :path: "./LocalPods/SLNetworking"
  SLPagingView:
    :path: "./LocalPods/SLPagingView"
  SLPresent:
    :path: "./LocalPods/SLPresent"
  SLRouter:
    :path: "./LocalPods/SLRouter"
  SLSectionModule:
    :path: "./LocalPods/SLSectionModule"
  SLServiceProtocol:
    :path: "./LocalPods/SLServiceProtocol"
  SLShareImage:
    :path: "./LocalPods/SLShareImage"
  SLTool:
    :path: "./LocalPods/SLTool"
  SLUIKitObjc:
    :path: "./LocalPods/SLUIKitObjc"
  SLUserInfo:
    :path: "./LocalPods/SLUserInfo"
  SLWechatModule:
    :path: "./LocalPods/SLWechatShareModule"
  SLYJHollowPageControl:
    :path: "./LocalPods/SLYJHollowPageControl"
  Weibo_SDK:
    :git: https://github.com/sinaweibosdk/weibo_ios_sdk.git
    :tag: 3.4.0

CHECKOUT OPTIONS:
  apaSdk:
    :commit: 7358b2ce571658358326ff79ae5e1dbf12d9c9c1
    :git: "****************:deepal/vehicleControlSDK/changan-ev-ios-apa-sdk.git"
  Block-KVO:
    :git: https://github.com/Tricertops/Block-KVO.git
    :tag: v3.0
  Factory:
    :git: https://github.com/hmlongco/Factory.git
    :tag: 2.3.2
  IGListDiffKit:
    :git: https://github.com/Instagram/IGListKit.git
    :tag: 5.0.0
  IGListKit:
    :git: https://github.com/Instagram/IGListKit.git
    :tag: 5.0.0
  PermissionsKit:
    :git: https://github.com/sparrowcode/PermissionsKit.git
    :tag: '11.1'
  RpaSdk:
    :git: https://e.coding.net/deepal/vehicleControlSDK/changan-ev-ios-Rpa-sdk.git
    :tag: 3.20.029-20250331102828
  ScreenMapping:
    :commit: 26bfefdc1b027b18ad4604c6692b402c3058a666
    :git: https://e.coding.net/deepal/vehicleControlSDK/ScreenMapping.git
  Weibo_SDK:
    :git: https://github.com/sinaweibosdk/weibo_ios_sdk.git
    :tag: 3.4.0

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Alamofire: 1c4fb5369c3fe93d2857c780d8bbe09f06f97e7c
  AlipaySDK-iOS: 9f81bf87e55750c3b3ac6c1a89c7cbf16ba06f5f
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  AMapNavi: 3e540c658ef083e92d8cc002ff2d45a4ffc010e7
  AMapSearch: 1f24a4acee521d993a8190348d27f5765841e32a
  apaSdk: 83ac9a6b487a6c75579c7c069af52ea0d0473418
  AvoidCrash: 28c6916fe19bd2b84126b8dec7cbe61c9a12741d
  Block-KVO: 6e489d53756d5cb03128ed420fb1f1085522007f
  BusinessRoute: de180677012246f9b470f47160b7e1cd36a24fa2
  CAAFOnlineSDK: ddaee8649972cd2249be125553a5de2d3cb2443a
  Cache: 9507fbf2a7b4da126ecf1f44d4255ee283a22527
  CACoreBridge: 7c8c726fb28bc1a0a890e111362d4b6fa486a052
  CACustomerServiceModule: 948f41c596a3edce8bcf6bd0fbc92301ebfd15f0
  CADefines: a3ccbeae6f215e15e2579e7a576707e8bc0242d6
  CALaunchAd: c501921c8b26590b51e686fd27ca78ccee92c036
  CALocation: 0a2a271c1595f80894cd615d4890bdbb406420da
  CarControlManager: de530b714ddd9366bba2ec72aa82acc22bc030b9
  CarControlSDK: 5c08ee8717419672b9ad4c1d1827de35680530b0
  CertificateSigningRequest: de2920a410a9d3737d636fbb89b9fe77b6f1f4a1
  Charts: ce0768268078eee0336f122c3c4ca248e4e204c5
  CleanJSON: 910a36465ce4829e264a902ccf6d1455fdd9f980
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CocoaLumberjack: 543c79c114dadc3b1aba95641d8738b06b05b646
  CocoaMQTT: 1f206228b29318eabdacad0c2e4e88575922c27a
  CryptoSwift: 967f37cea5a3294d9cce358f78861652155be483
  CTAssetsPickerController: b1d1d50ab87cf6b8b13531de5f4530482e7e53ed
  DeepalAssets: ccf88604c928f3a9b98674f2de0e6f1948cc5dc9
  DeepalCommunity: a5baa71becc32ca396c6666dc84b9eef5ae8bc3c
  DeepalFilter: da1a14728e02f983b44fba223f33037507a74a8b
  DeepalLogin: 4d4cbe66eeb8c05f3c8ca642919368c88e900182
  DeepalPhotoBrowser: 64bed85c6f91a8e6ad4e816654e981ef6a944560
  DeepalPlayer: abb2db4263dbf5a17655b39aabbd527d4c375202
  DeepalSearch: e38636567ab07ef9722f0e59d29f0199bd49edd8
  DeepalShield: 3edef6b45672cc00f20c84300e81c59e13b6ce5a
  DeepalSlider: d032f6da0c362c134735876281bcd21aac4b9605
  Differentiator: e8497ceab83c1b10ca233716d547b9af21b9344d
  dsBridge: 9641ccf9588254ef4eb8e7a6cf7f1b4fbfadf456
  DSF_QRCode: 6d4bd02a7cb4a7c0fb632cf7e162293412973b73
  EmptyDataSet-Swift: eb382c0c87a2d9c678077385a595cec52da38171
  Factory: 3a9d0809910130d609b34d4554828e6ecce519ff
  fishhook: ea19933abfe8f2f52c55fd8b6e2718467d3ebc89
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  Foil: ea199c32c4ca6d5ba447a6ca13bec5fa458f5bc0
  FSPagerView: 670405b2f18e2a87fa37f20b00de783e562c25a8
  FunctionalObjC: bd6aaa4b69abea0a5ac0e860a052c1ebebd5311c
  GKCycleScrollView: 8ed79d2142e62895a701973358b6f94b661b4829
  GKPhotoBrowser: 97f41f9349c1c74bb9ef742ff40840b57b347d1b
  GKSliderView: 7c6f853afeb0ae370bb8dce76ec669d6e9109d1d
  GrowingAnalytics: 8f9c45007692a6cb29e915c83826228c94668a40
  GrowingAnalytics-cdp: 9cc14f2607040676eee4126fff104c27fd63088c
  HandyJSON: 9e4e236f5d2dbefad5155a77417bbea438201c03
  HUDExtension: 23e134c45958565846eafa50aac1ff25c65afa96
  IconFont: 97ff724bc6d26aac018678a2da4f8542600e5a67
  IGListDiffKit: 02d937d7e714d6f558771f284ed80668d483b522
  IGListKit: baf7e87b4c2262cd569c380e46ca5667a4a82c82
  IQKeyboardManager: 45a1fa55c1a5b02c61ac0fd7fd5b62bb4ad20d97
  JXCategoryView: 7b1ee69ede4843c581688afe84d0f047723262f2
  JXSegmentedView: cd73555ce2134d1656db2cb383ba9c2f36fb5078
  KeychainAccess: c0c4f7f38f6fc7bbe58f5702e25f7bd2f65abf51
  Kingfisher: 53a10ea35051a436b5fb626ca2dd8f3144d755e9
  libpag: 154c2213708c16c48a49b3dd51c85399cebb6a5a
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  LookinServer: 1b2b61c6402ae29fa22182d48f5cd067b4e99e80
  lottie-ios: 016449b5d8be0c3dcbcfa0a9988469999cd04c5d
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MemoryCache: d265f22968b68180e089bf2872fe0052cdff1f1d
  MetalPetal: 001513fa0b0f6d4f147aa2651d40c594f2eacb01
  MGJRouter_Swift: 34ce06f777d8758edc08c2b7fc92934478a360d7
  MJExtension: 21c5f6f8c4d5d8844b7ae8fbae08fed0b501f961
  MJRefresh: fdf5e979eb406a0341468932d1dfc8b7f9fce961
  MMKV: 6c208ddcb4c197e564f72f3f73ef3b0c792e091c
  MMKVCore: 9cfef4c48c6c46f66226fc2e4634d78490206a48
  Moya: 138f0573e53411fb3dc17016add0b748dfbd78ee
  MqttCocoaAsyncSocket: 77d3b74f76228dd5a05d1f9526eab101d415b30c
  OpenSSL-Universal: 6082b0bf950e5636fe0d78def171184e2b3899c2
  PanModal: 833ec9e3470845126984ef1efb37856b94da2052
  PermissionsKit: e1d7aac77274fdcd95f3d2495292185666eccc53
  PinLayout: f6c2b63a5a5b24864064e1d15c67de41b4e74748
  Prelude: 04f3404078fdad4fb1a5a89f102bc9fd3158c1a4
  Protobuf: 2e6de032ba12b9efb390ae550d1a243a5b19ddfc
  PureLayout: 4d550abe49a94f24c2808b9b95db9131685fe4cd
  QAPM: 64695bb16bed9f841a77986dfe52dd972e500c9f
  QCloudCore: a1c543624fe34c5e98c92b7faf421a23f57ad5e3
  QCloudCOSXML: 8185a7a51c7dce6ff1a25c725907f6beb270cd01
  QCloudTrack: 6bf7aaa7e870d72fb4c9c88455dc63c7c65073b7
  ReactiveObjC: 011caa393aa0383245f2dcf9bf02e86b80b36040
  ReactorKit: e8b11d6b9c415405f381669b095c154a05b59eca
  RpaSdk: 4c36a0321d16dcd2cc25e9aea091ddbc641aa9a9
  RxCocoa: fb0aefb9e7229c8ab057acde714136d19117f102
  RxDataSources: aa47cc1ed6c500fa0dfecac5c979b723542d79cf
  RxExtension: eba029f1e172e9865ef31f7c93ef0098e5bd3a3c
  RxRelay: 5d39f4d8da150ad47a71b9ce11915e3eae2a8f4d
  RxSwift: 6ee35684dc3f7a63938758f4b7708199517dfa97
  RxSwiftExt: 43aaacb6a4d3c69c55e9d1cf4f79920043d13583
  RxViewController: 56486fa0afd629a1d6b5ba077f3b8cd9dc31b5d2
  ScreenMapping: 132309e4def3b5cf90e9bd5c407f200ea65d93e6
  SDCycleScrollView: a0d74c3384caa72bdfc81470bdbc8c14b3e1fbcf
  SDWebImage: f9258c58221ed854cfa0e2b80ee4033710b1c6d3
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  ServiceModule: 42eefbd0efd70e8a11c8b40e4351f1977fa091f5
  SFImageMaker: d909d7a2607669b390d3e38c052b95bceaf908cb
  SLActivity: 0d3e4f5478153f07828c5e143d3a68695e4cd980
  SLBaseConfig: e14b572e8418ebb0eafb8b53855d390716124e76
  SLBaseModel: 36afcc41c61d91827f77f2d906ffdcc28db072e1
  SLBaseUI: 509e2d6b590f34ba58279926351c2be4245fe9d8
  SLBaseViewKit: dd2c16dc9aaef6494b035f4724eaf25da3151dae
  SLBuffer: dfeb3ba3bc635e2a6ec4967c807c8a393dc9b751
  SLBuryPointConfig: ae457ceb4494dc628873e95f26db3c0a549ff9ef
  SLCarModel3DPlayer: fa2aac575eee6c7b136f396e422ecec4db5b61b3
  SLCFSPagerView: bc790bac2c0e836e212b8b8663e2e1a9698bd2d7
  SLCleanJSONDecoder: 9aee61553f9f9f419cfebede22462793f2531254
  SLColor: 45040fce6493c54a184699d0bc2c2e84a8c6a48f
  SLEmptyViewManager: fe6142ea751b41d02ee187ca19512043e974348c
  SLEnvConfig: 40f91a2abc51952195026604abb5ee915dafc3d8
  SLFoundation: c7aac139d9a820d8170fef36634b9eadb2edc4aa
  SLGeolocationService: 6be4838bd1828127ddfcfc7b5c701b281f695679
  SLGzip: 1558cf9c47ccceb5612c0b9700ba5f7acb77114b
  SLIconFont: a9a630e5800e2ceae5835a0e0c2430c9c86ab816
  SLKeyChain: f16d0cd53239ae3753996150568bde6a2f8dbf53
  SLLogConfig: a18aa3bf168f586ffdb272fa22442e1a0decf937
  SLLogger: 6237f21cb8c05b8192f5d9633d952044d8e7251a
  SLLogPlugin: 8376efab90068b34306ece8f21cb265a31be419f
  SLMallModule: 6fdc535ba68396db7f4eb48259b8d1806022136c
  SLMapView: 7e5af08a94b1b40f12f8d1d2f79aaee3a59ab83e
  SLMQTT: 0623433df993531edf3b321804c13a034fe4cda4
  SLMQTTModel: b7ad7e52b785ea278c663daca83f43d3c553b3e9
  SLNetworking: 1e8c512f8cb7e2e9a28ddeb0b97691f12a2ce4b9
  SLPagingView: 112576917ec396f6297cd982b06ffa73c6420a20
  SLPopViewManager: ad29f13543b2f6c9eb501054c58e63ced480c9a7
  SLPresent: f0cc52c5777082e8b99d68d2d2bb964dde550879
  SLRouter: d5aa3813cb01d7125519a920eb113b24877e6f02
  SLRxSwiftExtension: f91e764f604ba663e908f58f45db7b495b39876b
  SLSafeUtils: 21182900de0051176145fcd1e7447a2bec428306
  SLSectionModule: efdd5e72e636c0cb09c6bb428dea6eed6303e277
  SLServiceProtocol: 8c701475c3ec048911893eb8bf7961b2c256be69
  SLShareImage: f7b1406313bbd1b7b1e62ee839ee504aa9fe32d4
  SLShareViewModule: 756102bd23154849168742840baab62b18fcb20b
  SLSimpleNetworking: c6d22011617c230b2c52791eeed2487a116ed50a
  SLTokenManager: 6937f4e096093e001537cebc57ba2ba859994a31
  SLTool: 6b50d0bd4b60624e2f830a791c9cac73b0b6c897
  SLUIKitObjc: c8e6135fbbc0e23fa518cef9828f738678d4f95d
  SLUnityFramework: 9b2992fdc0c62d07f2e37559456b7350bc97158e
  SLUserDefaultsStore: 3da456986bd6d0e8dc176bc7bdd4b25e9b99032e
  SLUserInfo: c0ae20c455dd582b4783a23056c34f7b76ae0bf2
  SLUtils: a8300ab95e148e2a6615378834e88b26393edc24
  SLWechatModule: b57fd99644893da1a981a942c075b8ab78d954ad
  SLWidgetControl: a60ab2e5cce981d85f574e8e84e39ce9045ac2bc
  SLYJHollowPageControl: 8dd90a24ee3ba57367aa4f8c1285949e93443dad
  SnapKit: e01d52ebb8ddbc333eefe2132acf85c8227d9c25
  SSignalKit: 15540295b494bcf5912ede9910fbf63444f28926
  SSZipArchive: fe6a26b2a54d5a0890f2567b5cc6de5caa600aef
  SVGKit: 1ad7513f8c74d9652f94ed64ddecda1a23864dea
  SVProgressHUD: 1428aafac632c1f86f62aa4243ec12008d7a51d6
  SwiftAlgorithms: 38dda4731d19027fdeee1125f973111bf3386b53
  SwiftDate: bbc26e26fc8c0c33fbee8c140c5e8a68293a148a
  SwifterSwift: dd00873fb09cde19da88bdb2878f9fe70fe27b0f
  SwiftGen: 4993cbf71cbc4886f775e26f8d5c3a1188ec9f99
  SwiftImageReadWrite: f58e807c5e3e211b788b5e058c4da464096ed430
  SwiftProtobuf: b7aa08087e2ab6d162862d143020091254095f69
  SwiftQRCodeGenerator: cc02fed209335064d0b0dd61b2a0874b9bc6bd5a
  SwiftyJSON: f5b1bf1cd8dd53cd25887ac0eabcfd92301c6a5a
  SwiftyRSA: 8c6dd1ea7db1b8dc4fb517a202f88bb1354bc2c6
  TABAnimated: 09bf3076879e91f33afc6894c2d688e6d3309766
  TagListView-ObjC: 432991e24c5177eb4fa7d721de7084f09f39a0b8
  Then: 844265ae87834bbe1147d91d5d41a404da2ec27d
  Toast-Swift: 7a03a532afe3a560d4044bc7c237e2864d295173
  TPNS-iOS: 36c335eff80670de6ede780ab827f679d78f64ff
  TPNS-iOS-Extension: f7a975a9d0f8c6d197fe8cacedb3c0e120107af7
  TTGTagCollectionView: 0d4625aaf7ee3ad755c39d549368a4ce69bd17d1
  TXLiteAVSDK_TRTC: aa8b139da59f99c34cca0ce7a755c5083dc7a676
  TZImagePickerController: d084a7b97c82d387e7669dd86dc9a9057500aacf
  UAVPSDK: 7ded5cf5176662fd48bbe62c4cb81d81b5ac40ec
  UIComponent: 13e92f476d1cb75d36b042696b6e969392e5c1f9
  "UITableView+FDTemplateLayoutCell": 5c949b4a5059c404b442926c0e80f81d10a2d66f
  UITextView-WZB: aa7306ec35ad35d325f8e608366ecd5ac8c8eed9
  URLNavigator: cb50b445dc873085cfa7d2cb1a31d945ac18ce3a
  VideoDecoder: 53b676cfa79a79912d8a4d6efcab54d076940461
  WeakMapTable: 05c694ce8439a7a9ebabb56187287a63c57673d6
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f
  Weibo_SDK: ea4d83cd1b435635c402f73a3c441ee1f35fa87a
  YBAttributeTextTapAction: b7e61f0265b5673e7ce7bad7319e8eff9e209a2b
  YPNavigationBarTransition: d3d8c0e4cced41dec43647832bb45737d79cc8b0
  YYCache: 8105b6638f5e849296c71f331ff83891a4942952
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  YYText-Nice: e92fecc1ac47457d0259d9c37b34ff973da28c2c
  ZFPlayer: 5cf39e8d9f0c2394a014b0db4767b5b5a6bffe13

PODFILE CHECKSUM: 264c7f19e83b794b7b336f8620861ad4af43be87

COCOAPODS: 1.16.2
