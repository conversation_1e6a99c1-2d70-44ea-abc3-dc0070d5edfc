# Uncomment the next line to define a global platform for your project
source '****************:deepal/vehicleControlSDK/SLRepos.git'
source 'https://github.com/CocoaPods/Specs.git'
source 'https://github.com/TencentCloud/QAPM-iOS-CocoaPods.git'
source 'https://e.coding.net/deepal/digital-marketing/changan-ev-ios-specs.git'

min_deployment_target = '13.0'
platform :ios, min_deployment_target
use_frameworks!

target 'CAQC' do
  # Pods for CAQC
  pod 'AFNetworking',                     '4.0.1'
  pod 'YYModel',                          '1.0.4'
  pod 'YYText-Nice',                      '1.1.0'
  pod 'SDWebImage',                       '5.18.7'
  pod 'ReactiveObjC',                     '3.1.1'
  pod 'MJRefresh',                        '3.7.5'
  pod 'MBProgressHUD',                    '1.2.0'
  pod 'IQKeyboardManager',                '6.5.10'
  pod 'fishhook',                         '0.2'
  pod 'YYCache',                          '1.0.4'
  pod 'CocoaLumberjack',                  '3.7.4'
  pod 'Masonry',                          '1.1.0'
  pod 'UITextView-WZB',                   '1.1.1'
  pod 'IconFont',                         '1.0.2'
  pod 'GKPhotoBrowser',                   '2.6.3'
  pod 'YPNavigationBarTransition',        '2.2.3'
  pod 'AlipaySDK-iOS',                    '15.8.10'
  pod 'WechatOpenSDK-XCFramework',        '2.0.4'
  pod 'QCloudCOSXML',                     '6.4.5'
  pod 'SFImageMaker',                     '1.1.3'
  pod 'Block-KVO', git: 'https://github.com/Tricertops/Block-KVO.git', tag: 'v3.0'
  pod 'TPNS-iOS',                         '*******'
  pod 'TPNS-iOS-Extension',               '*******'
  pod 'FunctionalObjC',                   '1.0.2'
  pod 'TagListView-ObjC',                 '0.1.1'
  pod 'GrowingAnalytics/DISABLE_IDFA',    '3.4.1'
  pod 'GrowingAnalytics/Protobuf',        '3.4.1'
  pod 'GrowingAnalytics-cdp/Autotracker', '3.4.1'
  pod 'AMapLocation',                     '2.10.0'
  pod 'AMapSearch',                       '9.7.0'
  pod 'AMapNavi',                         '10.0.600'
  pod 'SnapKit',                          '5.6.0'
  pod 'SVProgressHUD',                    '2.2.5'
  pod 'HandyJSON',                        '5.0.2'
  pod 'CocoaMQTT',                        '2.1.6'
  pod 'TXLiteAVSDK_TRTC',                 '11.6.15007'
  pod 'SSZipArchive',                     '2.4.3'
  pod 'UITableView+FDTemplateLayoutCell', '1.6'
  pod 'TTGTagCollectionView',             '2.4.2'
  # 头部悬停滑动分页组件
  pod 'JXCategoryView',                   '1.6.1'
  pod 'JXSegmentedView',                  '1.4.1'
  pod 'libpag',                           '4.4.25'
  pod 'MJExtension',                      '3.4.1'
  pod 'QAPM', '5.3.3'
  pod 'CTAssetsPickerController',          '3.3.1'
  pod 'SDCycleScrollView',                 '1.82'
  pod 'TZImagePickerController',          '3.8.8'
  pod 'KeychainAccess',                   '4.2.2'
  pod 'MMKV',                           '1.2.5'
  pod 'CocoaAsyncSocket',               '7.6.5'
  pod 'Toast-Swift',                    '5.1.1'
  pod 'MGJRouter_Swift',                '0.1.3'
  pod 'FMDB',                           '2.7.5'
  pod 'FSPagerView', '0.8.3'
  pod 'CarControlSDK', '0.4.11.0524.01' # :git => '****************:deepal/vehicleControlSDK/ios.git', :branch => 'develop/OS3.1'
  #  pod 'apaSdk'        ,  :git => '****************:deepal/vehicleControlSDK/changan-ev-ios-apa-sdk.git'  , :tag => 'SDK2025032001.01'
  pod 'apaSdk', git: '****************:deepal/vehicleControlSDK/changan-ev-ios-apa-sdk.git',
                branch: 'dev_318'
  pod 'dsBridge', '4.0.0'

  pod 'RpaSdk', git: 'https://e.coding.net/deepal/vehicleControlSDK/changan-ev-ios-Rpa-sdk.git',
                tag: '3.20.029-20250331102828'
  # 华为返控投屏
  pod 'ScreenMapping', git: 'https://e.coding.net/deepal/vehicleControlSDK/ScreenMapping.git',
                       branch: 'master'
  pod 'ZFPlayer',                         '4.1.4'
  pod 'ZFPlayer/ControlView',             '4.1.4'
  pod 'ZFPlayer/AVPlayer',                '4.1.4'
  pod 'SLEmptyViewManager', '1.3.3'
  pod 'CACustomerServiceModule', '1.1.7'
  pod 'CADefines',    '1.1.7'
  pod 'CACoreBridge', '1.2.1'
  pod 'CALaunchAd', '1.1.1'
  pod 'CAAFOnlineSDK'
  pod 'SLUtils', '1.5.6'
  pod 'AvoidCrash', '~> 2.5.2', configurations: ['Release']
  pod 'Weibo_SDK', git: 'https://github.com/sinaweibosdk/weibo_ios_sdk.git', tag: '3.4.0'

  pod 'IGListKit', git: 'https://github.com/Instagram/IGListKit.git', tag: '5.0.0'
  pod 'IGListDiffKit', git: 'https://github.com/Instagram/IGListKit.git', tag: '5.0.0'
  pod 'EmptyDataSet-Swift', '~> 5.0.0'
  pod 'YBAttributeTextTapAction'
  # - SL
  pod 'SLKeyChain',   '0.1.1'
  pod 'SLLogConfig',  '0.2.0'

  pod 'SLBaseModel',  path: './LocalPods/SLBaseModel'
  pod 'DeepalLogin', path: './LocalPods/DeepalLogin'
  pod 'SLIconFont', path: './LocalPods/SLIconFont'
  pod 'SLActivity', path: './LocalPods/SLActivity'
  pod 'SLShareImage', path: './LocalPods/SLShareImage'
  pod 'DeepalPlayer', path: './LocalPods/DeepalPlayer'

  pod 'SLPagingView', path: './LocalPods/SLPagingView'
  pod 'SLPresent', path: './LocalPods/SLPresent'
  pod 'Prelude', '1.1.1'
  pod 'ReactorKit'
  pod 'SLYJHollowPageControl', path: './LocalPods/SLYJHollowPageControl'
  pod 'SLUIKitObjc', path: './LocalPods/SLUIKitObjc'
  pod 'SLWechatModule', path: './LocalPods/SLWechatShareModule'
  pod 'DeepalSlider', path: './LocalPods/DeepalSlider'
  pod 'DeepalPhotoBrowser', path: './LocalPods/DeepalPhotoBrowser'
  pod 'DeepalCommunity', path: './LocalPods/DeepalCommunity'
  pod 'DeepalAssets', path: './LocalPods/DeepalAssets'
  pod 'DeepalShield', path: './LocalPods/DeepalShield'

  pod 'SLFoundation', '0.2.5'
  pod 'PanModal', path: './LocalPods/PanModal'
  pod 'SLUserInfo', path: './LocalPods/SLUserInfo'
  pod 'SLServiceProtocol', path: './LocalPods/SLServiceProtocol'
  pod 'SLBuryPointConfig', path: './LocalPods/SLBuryPointConfig'
  pod 'SLGeolocationService', path: './LocalPods/SLGeolocationService'
  pod 'DeepalSearch', path: './LocalPods/DeepalSearch'
  pod 'PinLayout', '1.10.5'
  pod 'UIComponent', '4.1.4'

  pod 'Factory', git: 'https://github.com/hmlongco/Factory.git', tag: '2.3.2'

  pod 'RxExtension', path: './LocalPods/RxExtension'
  pod 'SLBaseUI', path: './LocalPods/SLBaseUI'

  pod 'SLColor', path: './LocalPods/SLColor'
  pod 'HUDExtension', path: './LocalPods/HUDExtension'

  pod 'SLNetworking', path: './LocalPods/SLNetworking'
  pod 'SLNetworking/Cache', path: './LocalPods/SLNetworking'
  pod 'SLNetworking/Plugin', path: './LocalPods/SLNetworking'
  pod 'SLNetworking/Codable', path: './LocalPods/SLNetworking'

  pod 'Cache', '7.4.0'
  pod 'PermissionsKit', git: 'https://github.com/sparrowcode/PermissionsKit.git', tag: '11.1'
  pod 'PermissionsKit/CameraPermission', git: 'https://github.com/sparrowcode/PermissionsKit.git', tag: '11.1'
  pod 'PermissionsKit/PhotoLibraryPermission', git: 'https://github.com/sparrowcode/PermissionsKit.git', tag: '11.1'
  pod 'PermissionsKit/NotificationPermission', git: 'https://github.com/sparrowcode/PermissionsKit.git', tag: '11.1'
  pod 'PermissionsKit/MicrophonePermission', git: 'https://github.com/sparrowcode/PermissionsKit.git', tag: '11.1'
  pod 'PermissionsKit/LocationPermission', git: 'https://github.com/sparrowcode/PermissionsKit.git', tag: '11.1'
  pod 'PermissionsKit/BluetoothPermission', git: 'https://github.com/sparrowcode/PermissionsKit.git', tag: '11.1'

  pod 'URLNavigator', '2.6.0'
  pod 'SLRouter', path: './LocalPods/SLRouter'

  pod 'BusinessRoute', path: './LocalPods/BusinessRoute'
  pod 'SLMapView', path: './LocalPods/SLMapView'
  pod 'MemoryCache', path: './LocalPods/MemoryCache'

  #  pod 'SelectStore', path:'./LocalPods/SelectStore'
  pod 'SLSectionModule', path: './LocalPods/SLSectionModule'
  pod 'SLSectionModule/JX', path: './LocalPods/SLSectionModule'
  pod 'ServiceModule', path: './LocalPods/ServiceModule'

  pod 'SLMallModule', path: './LocalPods/SLMallModule'
  pod 'SLTool', path: './LocalPods/SLTool'
  pod 'CALocation', path: './LocalPods/CALocation'
  pod 'SLBaseConfig', path: './LocalPods/SLBaseConfig'
  pod 'LookinServer', subspecs: ['SwiftAndNoHook'], configurations: ['Debug']
  pod 'RxSwift', '6.7.0'
  pod 'RxCocoa', '6.7.0'
  pod 'RxSwiftExt', '6.2.1'
  pod 'RxDataSources', '5.0.0'
  pod 'Foil', '5.0.1'
  pod 'SwiftDate', '7.0.0'
  pod 'SwifterSwift/UIKit', '6.2.0'
  pod 'SwifterSwift/CoreAnimation', '6.2.0'
  pod 'SwifterSwift/CoreGraphics', '6.2.0'
  pod 'SwifterSwift/Dispatch', '6.2.0'
  pod 'SwifterSwift/SwiftStdlib', '6.2.0'
  pod 'SwifterSwift/Foundation', '6.2.0'
  pod 'SLBaseViewKit', '1.2.0'
  pod 'SLEnvConfig', '1.0.6'
  pod 'SLPopViewManager', '1.0.5'
  pod 'SLWidgetControl', '1.6.0'

  pod 'SwiftGen', '6.6.3'
  pod 'DeepalFilter', path: './LocalPods/DeepalFilter'
  pre_install do |_installer|
    system("ruby #{File.dirname(__FILE__)}/sh/GenerateAsset.rb")
  end

  post_install do |installer|
    installer.generated_projects.each do |project|
      project.targets.each do |target|
        target.build_configurations.each do |config|
          if %w[lottie-ios SnapKit SwiftProtobuf].include?(target.name)
            config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
          end

          config.build_settings['SWIFT_COMPILATION_MODE'] = 'incremental' if target.name == 'HandyJSON'

          config.build_settings['SWIFT_INSTALL_OBJC_HEADER'] = 'NO' if target.name == 'MGJRouter_Swift'

          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = min_deployment_target
        end
      end
    end
  end
end

target 'CarControlWidgetExtension' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for CarControlWidgetExtension
  pod 'Alamofire', '5.5.0'
  pod 'SLLogger', '1.1.3'
  pod 'SLLogPlugin'
  pod 'Kingfisher', '7.12.0'
  pod 'SLUtils'
  pod 'CleanJSON'
  pod 'SLEnvConfig'
  pod 'SLCleanJSONDecoder'
  pod 'RxSwift'
  pod 'SLWidgetControl'
end

target 'XGExtension' do
  platform :ios, '13.0'
  pod 'TPNS-iOS-Extension', '*******' # 需要与主SDK（TPNS-iOS）版本保持一致
end

target 'SLWatch' do
  platform :watchos, '7.0'
  use_frameworks!

  # Pods for SLWatch Watch App
  SLNetworkingName = 'SLSimpleNetworking'
  SLNetworkingVersion = '1.1.6'
  pod SLNetworkingName, SLNetworkingVersion
  pod SLNetworkingName + '/SLNetworkingCodable', SLNetworkingVersion
  pod SLNetworkingName + '/SLNetworkingPlugin', SLNetworkingVersion
  pod 'CarControlManager', '1.9.2'
  pod 'DSF_QRCode', '~> 21.2.0'
  pod 'SLLogger', '>=1.1.2'
  pod 'SLLogPlugin'
  pod 'SSZipArchive'
  pod 'SLMQTTModel', '1.1.6'
  pod 'SLEnvConfig'
  pod 'RxSwift'
  pod 'SLTokenManager'
  pod 'SLCleanJSONDecoder'
  pod 'SLUtils'
  pod 'SLSafeUtils', '>=1.0.3'
end

target 'ScreenMapping' do
  platform :ios, '13.0'
  use_frameworks!

  pod 'ScreenMapping', git: 'https://e.coding.net/deepal/vehicleControlSDK/ScreenMapping.git',
                       branch: 'master'
end

target 'SiriItentsExtension' do
  pod 'SLWidgetControl'
end
