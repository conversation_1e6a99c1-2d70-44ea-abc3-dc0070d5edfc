//
//  SettingReactor.swift
//  CAQC
//
//  Created by FD on 3/24/25.
//

import CAAutonym
import CAToolBase
import DeepalShield
import ReactorKit
import RxCocoa
import RxSwift
import SLBaseUI
import SLEnvConfig
import SLFoundation
import SLServiceProtocol
import SwiftyJSON
import UIComponent

// MARK: - SettingReactor

final class SettingReactor: Reactor {
  // MARK: Lifecycle

  // MARK: - Initialization

  init(memberService: CAMemberViewModelService = CAMemberViewModelService()) {
    self.memberService = memberService
  }

  // MARK: Internal

  // MARK: - Types

  enum Action {
    case viewDidLoad
    case itemTapped(SettingItemType)
    case logout
    case refreshData
  }

  enum Mutation {
    case setLoading(Bool)
    case setItems([SettingItem])
    case updateCacheSize(String)
    case updateVersionInfo(tips: String?, url: String?)
    case insertCarVerificationItem
    case setError(Error?)
    case setLogoutLoading(Bool)
  }

  struct State {
    var isLoading: Bool = false
    var items: [SettingItem] = []
    var error: Error?
    var isLogoutLoading: Bool = false
  }

  let initialState = State()

  // MARK: - Reactor Methods

  func mutate(action: Action) -> Observable<Mutation> {
    switch action {
    case .viewDidLoad:
      return Observable.concat([
        Observable.just(.setLoading(true)),
        loadInitialData(),
        Observable.just(.setLoading(false)),
      ])

    case let .itemTapped(itemType):
      return handleItemTap(itemType)

    case .logout:
      return performLogout()

    case .refreshData:
      return Observable.concat([
        Observable.just(.setLoading(true)),
        loadInitialData(),
        Observable.just(.setLoading(false)),
      ])
    }
  }

  func reduce(state: State, mutation: Mutation) -> State {
    var newState = state

    switch mutation {
    case let .setLoading(isLoading):
      newState.isLoading = isLoading

    case let .setItems(items):
      newState.items = items

    case let .updateCacheSize(cacheSize):
      if let index = newState.items.firstIndex(where: { $0.type == .clearCache }) {
        let updatedItem = SettingItem(
          type: .clearCache,
          title: "清除缓存",
          detail: cacheSize
        )
        newState.items[index] = updatedItem
      }

    case let .updateVersionInfo(tips, url):
      if let index = newState.items.firstIndex(where: { $0.type == .versionUpdate }) {
        let updatedItem = SettingItem(
          type: .versionUpdate,
          title: "版本更新",
          detail: currentVersion(),
          tips: tips,
          url: url
        )
        newState.items[index] = updatedItem
      }

    case .insertCarVerificationItem:
      let carVerificationItem = SettingItem(
        type: .carVerification,
        title: "车辆认证",
        needLogin: true
      )
      newState.items.insert(carVerificationItem, at: 1)

    case let .setError(error):
      newState.error = error

    case let .setLogoutLoading(isLoading):
      newState.isLogoutLoading = isLoading
    }

    return newState
  }

  // MARK: Private

  private let memberService: CAMemberViewModelService
  private let disposeBag = DisposeBag()
}

// MARK: - Private Methods

private extension SettingReactor {
  func loadInitialData() -> Observable<Mutation> {
    let initialItems = makeInitialItems()

    let cacheObservable = fetchCacheSize()
    let versionObservable = fetchVersionInfo()
    let carVerificationObservable = fetchCarVerificationSwitch()

    return Observable.concat([
      Observable.just(.setItems(initialItems)),
      cacheObservable,
      versionObservable,
      carVerificationObservable,
    ])
  }

  func makeInitialItems() -> [SettingItem] {
    var items: [SettingItem] = []

    // Personal Info
    items.append(SettingItem(
      type: .personalInfo,
      title: "个人资料",
      needLogin: true
    ))

    // Notification Settings
    items.append(SettingItem(
      type: .notificationSettings,
      title: "通知设置",
      needLogin: true
    ))

    // Permission Settings
    items.append(SettingItem(
      type: .permissionSettings,
      title: "权限设置"
    ))

    // Privacy Management (conditional)
    if CALoginUserInfoObj.sharedInstance().isLogin, ShareConfig.needShield {
      items.append(SettingItem(
        type: .privacyManagement,
        title: "隐私管理"
      ))
    }

    // Clear Cache
    items.append(SettingItem(
      type: .clearCache,
      title: "清除缓存",
      detail: ""
    ))

    // Account Security
    items.append(SettingItem(
      type: .accountSecurity,
      title: "账号安全",
      needLogin: true
    ))

    // About Deepal
    items.append(SettingItem(
      type: .aboutDeepal,
      title: "关于深蓝"
    ))

    // Version Update
    items.append(SettingItem(
      type: .versionUpdate,
      title: "版本更新",
      detail: currentVersion()
    ))

    return items
  }

  func fetchCacheSize() -> Observable<Mutation> {
    return Observable.create { observer in
      CAClearCacheTool.getCacheSize { cacheSize, succeed in
        if succeed, let cacheSize = cacheSize {
          observer.onNext(.updateCacheSize(cacheSize))
        }
        observer.onCompleted()
      }
      return Disposables.create()
    }
  }

  func fetchVersionInfo() -> Observable<Mutation> {
    return Observable.create { observer in
      CAAppManager.sharedManager().getGrayAppVersion { success, _ in
        if !success || CAAppManager.sharedManager().updateType == .none {
          observer.onCompleted()
          return
        }

        let title = CAAppManager.sharedManager().grayVersionModel.updateTitle
        let desc = CAAppManager.sharedManager().grayVersionModel.depiction
        let url = CAAppManager.sharedManager().grayVersionModel.trialVersionH5url

        if !title.isEmpty || !desc.isEmpty {
          let tips = url.isEmpty ? "新版本" : "参与体验"
          observer.onNext(.updateVersionInfo(tips: tips, url: url))
        }

        observer.onCompleted()
      }
      return Disposables.create()
    }
  }

  func fetchCarVerificationSwitch() -> Observable<Mutation> {
    return SettingAPI
      .realNameVerificationSwich
      .request()
      .compactMap { try? JSON(data: $0.data) }
      .catchAndReturn(JSON())
      .flatMap { json -> Observable<Mutation> in
        let verificationSwitch = json["data"]["value"].stringValue == "1"
        if verificationSwitch {
          return Observable.just(.insertCarVerificationItem)
        } else {
          return Observable.empty()
        }
      }
  }

  func handleItemTap(_: SettingItemType) -> Observable<Mutation> {
    // Navigation actions are handled in the view controller
    // This method can be used for any state changes needed after navigation
    return Observable.empty()
  }

  func performLogout() -> Observable<Mutation> {
    return Observable.concat([
      Observable.just(.setLogoutLoading(true)),
      Observable.create { observer in
        CAVehicleModuleService.cacheVehicleInfo(nil)
        CALoginManager.tpnsTokenUnBind { success in
          if success {
            self.memberService.logOut { flag in
              if flag {
                CALoginManager.userDidLogOut()
                observer.onNext(.setLogoutLoading(false))
                observer.onCompleted()
              } else {
                observer.onNext(.setLogoutLoading(false))
                observer.onNext(.setError(NSError(
                  domain: "LogoutError",
                  code: -1,
                  userInfo: [NSLocalizedDescriptionKey: "操作失败，请稍后重试"]
                )))
                observer.onCompleted()
              }
            }
          } else {
            observer.onNext(.setLogoutLoading(false))
            observer.onNext(.setError(NSError(
              domain: "LogoutError",
              code: -1,
              userInfo: [NSLocalizedDescriptionKey: "操作失败，请稍后重试"]
            )))
            observer.onCompleted()
          }
        }
        return Disposables.create()
      },
    ])
  }

  func currentVersion() -> String {
    if SLVersionType == "0" {
      return SLAppVersion + "-beta-" + SLReleaseNO
    }

    if SLVersionType == "1" {
      return CALoginUserInfoObj.numberStr()
    }

    if SLVersionType == "2" {
      return SLAppVersion + "-alpha-" + SLReleaseNO
    }
    return ""
  }
}
