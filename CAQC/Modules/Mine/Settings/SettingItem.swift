//
//  SettingItem.swift
//  CAQC
//
//  Created by FD on 3/24/25.
//

import Foundation

// MARK: - SettingItemType

enum SettingItemType: String, CaseIterable {
  case personalInfo = "个人资料"
  case carVerification = "车辆认证"
  case notificationSettings = "通知设置"
  case permissionSettings = "权限设置"
  case privacyManagement = "隐私管理"
  case clearCache = "清除缓存"
  case accountSecurity = "账号安全"
  case aboutDeepal = "关于深蓝"
  case versionUpdate = "版本更新"
}

// MARK: - SettingItem

struct SettingItem: Equatable {
  // MARK: Lifecycle

  init(
    type: SettingItemType? = nil,
    title: String,
    detail: String? = nil,
    tips: String? = nil,
    url: String? = nil,
    needLogin: Bool = false,
    tapHandler:(()-> Void)? = nil
  ) {
    self.type = type
    self.title = title
    self.detail = detail
    self.tips = tips
    self.url = url
    self.needLogin = needLogin
    self.tapHandler = tapHandler
  }

  // MARK: Internal

  let type: SettingItemType?
  let title: String
  let detail: String?
  let tips: String?
  let url: String?
  let needLogin: Bool
  let tapHandler:(()-> Void)?

  // MARK: Equatable

  static func == (lhs: SettingItem, rhs: SettingItem) -> Bool {
    return lhs.type == rhs.type &&
      lhs.title == rhs.title &&
      lhs.detail == rhs.detail &&
      lhs.tips == rhs.tips &&
      lhs.url == rhs.url &&
      lhs.needLogin == rhs.needLogin
  }
}
